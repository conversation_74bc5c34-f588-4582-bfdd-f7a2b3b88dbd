"""
Tests for the main server module.
"""
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import HTT<PERSON>Exception
from fastapi.testclient import <PERSON><PERSON>lient

from app.server import app, read_root, startup_db_client, shutdown_db_client


@pytest.mark.unit
class TestServerModule:
    """Test cases for the main server module."""

    def test_app_creation(self):
        """Test FastAPI app creation and configuration."""
        # Test that app is created
        assert app is not None
        assert app.title == "SimplrOps Copilot"

    def test_app_middleware_configuration(self):
        """Test CORS middleware configuration."""
        # Check that CORS middleware is configured
        middleware_stack = app.user_middleware
        cors_middleware = None
        
        for middleware in middleware_stack:
            if "CORSMiddleware" in str(middleware.cls):
                cors_middleware = middleware
                break
        
        assert cors_middleware is not None

    def test_app_routes_included(self):
        """Test that API routes are included."""
        # Check that routes are registered
        routes = [route.path for route in app.routes]
        
        # Should have root route
        assert "/" in routes
        
        # Should have API routes (from router)
        api_routes = [route for route in routes if route.startswith("/ollie")]
        assert len(api_routes) > 0

    async def test_read_root(self):
        """Test the root endpoint."""
        # Call the function
        result = await read_root()
        
        # Assertions
        assert result == {"status": "healthy"}

    @patch('app.server.connect_to_mongo')
    async def test_startup_db_client(self, mock_connect_to_mongo):
        """Test database startup event."""
        # Setup mock
        mock_connect_to_mongo.return_value = None
        
        # Call the function
        await startup_db_client()
        
        # Assertions
        mock_connect_to_mongo.assert_called_once()

    @patch('app.server.close_mongo_connection')
    async def test_shutdown_db_client(self, mock_close_mongo_connection):
        """Test database shutdown event."""
        # Setup mock
        mock_close_mongo_connection.return_value = None
        
        # Call the function
        await shutdown_db_client()
        
        # Assertions
        mock_close_mongo_connection.assert_called_once()

    @patch('app.server.connect_to_mongo')
    async def test_startup_db_client_error(self, mock_connect_to_mongo):
        """Test database startup event with error."""
        # Setup mock to raise exception
        mock_connect_to_mongo.side_effect = Exception("Database connection failed")
        
        # Call the function - should not raise exception
        try:
            await startup_db_client()
        except Exception:
            # If exception is raised, it should be handled gracefully
            pass
        
        # Verify connection was attempted
        mock_connect_to_mongo.assert_called_once()

    @patch('app.server.close_mongo_connection')
    async def test_shutdown_db_client_error(self, mock_close_mongo_connection):
        """Test database shutdown event with error."""
        # Setup mock to raise exception
        mock_close_mongo_connection.side_effect = Exception("Database close failed")
        
        # Call the function - should not raise exception
        try:
            await shutdown_db_client()
        except Exception:
            # If exception is raised, it should be handled gracefully
            pass
        
        # Verify close was attempted
        mock_close_mongo_connection.assert_called_once()


@pytest.mark.unit
class TestExceptionHandlers:
    """Test cases for exception handlers."""

    def test_http_exception_handler(self, client):
        """Test HTTP exception handler."""
        # Create a test endpoint that raises HTTPException
        @app.get("/test-http-exception")
        async def test_http_exception():
            raise HTTPException(status_code=400, detail="Test HTTP error")
        
        # Make request to trigger exception
        response = client.get("/test-http-exception")
        
        # Assertions
        assert response.status_code == 400
        assert response.json() == {"detail": "Test HTTP error"}

    def test_general_exception_handler(self, client):
        """Test general exception handler."""
        # Create a test endpoint that raises general exception
        @app.get("/test-general-exception")
        async def test_general_exception():
            raise ValueError("Test general error")
        
        # Make request to trigger exception
        response = client.get("/test-general-exception")
        
        # Assertions
        assert response.status_code == 500
        assert response.json() == {"detail": "Internal server error"}

    @patch('app.server.logger')
    def test_http_exception_handler_logging(self, mock_logger, client):
        """Test that HTTP exception handler logs errors."""
        # Create a test endpoint that raises HTTPException
        @app.get("/test-http-exception-logging")
        async def test_http_exception_logging():
            raise HTTPException(status_code=404, detail="Resource not found")
        
        # Make request to trigger exception
        response = client.get("/test-http-exception-logging")
        
        # Assertions
        assert response.status_code == 404
        # Note: Logging verification might need adjustment based on actual implementation

    @patch('app.server.logger')
    def test_general_exception_handler_logging(self, mock_logger, client):
        """Test that general exception handler logs errors."""
        # Create a test endpoint that raises general exception
        @app.get("/test-general-exception-logging")
        async def test_general_exception_logging():
            raise RuntimeError("Test runtime error")
        
        # Make request to trigger exception
        response = client.get("/test-general-exception-logging")
        
        # Assertions
        assert response.status_code == 500
        # Note: Logging verification might need adjustment based on actual implementation


@pytest.mark.integration
class TestServerIntegration:
    """Integration tests for the server."""

    def test_root_endpoint(self, client):
        """Test the root endpoint integration."""
        # Make request to root endpoint
        response = client.get("/")
        
        # Assertions
        assert response.status_code == 200
        assert response.json() == {"status": "healthy"}

    def test_cors_headers(self, client):
        """Test CORS headers are present."""
        # Make OPTIONS request to test CORS
        response = client.options("/")
        
        # Should not return error (CORS should be configured)
        assert response.status_code in [200, 405]  # 405 is also acceptable for OPTIONS

    def test_api_routes_accessible(self, client):
        """Test that API routes are accessible."""
        # Test that API routes exist (even if they require auth)
        response = client.post("/ollie/classify", json={"prompt": "test"})
        
        # Should get 403 (auth required) not 404 (route not found)
        assert response.status_code == 403

    def test_invalid_route(self, client):
        """Test invalid route returns 404."""
        # Make request to non-existent route
        response = client.get("/nonexistent-route")
        
        # Assertions
        assert response.status_code == 404

    def test_app_startup_and_shutdown_events(self):
        """Test that startup and shutdown events are registered."""
        # Check that startup and shutdown events are registered
        startup_handlers = app.router.on_startup
        shutdown_handlers = app.router.on_shutdown
        
        # Should have at least one startup and shutdown handler
        assert len(startup_handlers) > 0
        assert len(shutdown_handlers) > 0

    def test_middleware_order(self):
        """Test middleware is applied in correct order."""
        # Check middleware stack
        middleware_stack = app.user_middleware
        
        # Should have CORS middleware
        cors_found = False
        for middleware in middleware_stack:
            if "CORSMiddleware" in str(middleware.cls):
                cors_found = True
                break
        
        assert cors_found

    def test_app_configuration(self):
        """Test app configuration settings."""
        # Test app title
        assert app.title == "SimplrOps Copilot"
        
        # Test that app has required attributes
        assert hasattr(app, 'routes')
        assert hasattr(app, 'middleware')
        assert hasattr(app, 'exception_handlers')

    def test_exception_handlers_registered(self):
        """Test that exception handlers are registered."""
        # Check that exception handlers are registered
        exception_handlers = app.exception_handlers
        
        # Should have handlers for HTTPException and general Exception
        assert HTTPException in exception_handlers or 500 in exception_handlers
        assert Exception in exception_handlers or 500 in exception_handlers

    def test_api_documentation_accessible(self, client):
        """Test that API documentation is accessible."""
        # Test OpenAPI docs
        response = client.get("/docs")
        assert response.status_code == 200
        
        # Test OpenAPI schema
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        # Verify it's valid JSON
        openapi_schema = response.json()
        assert "openapi" in openapi_schema
        assert "info" in openapi_schema
        assert openapi_schema["info"]["title"] == "SimplrOps Copilot"

    def test_health_check_endpoint_details(self, client):
        """Test health check endpoint returns proper format."""
        # Make request to health check
        response = client.get("/")
        
        # Assertions
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
        
        data = response.json()
        assert isinstance(data, dict)
        assert "status" in data
        assert data["status"] == "healthy"

    @patch('app.server.connect_to_mongo')
    @patch('app.server.close_mongo_connection')
    def test_database_lifecycle_events(self, mock_close_mongo, mock_connect_mongo):
        """Test database connection lifecycle events."""
        # Setup mocks
        mock_connect_mongo.return_value = None
        mock_close_mongo.return_value = None
        
        # Create test client (triggers startup events)
        with TestClient(app) as test_client:
            # Startup should have been called
            mock_connect_mongo.assert_called()
            
            # Make a request to ensure app is working
            response = test_client.get("/")
            assert response.status_code == 200
        
        # Shutdown should have been called when context manager exits
        mock_close_mongo.assert_called()

    def test_request_response_cycle(self, client):
        """Test complete request-response cycle."""
        # Test various HTTP methods on root endpoint
        methods_and_expected = [
            ("GET", 200),
            ("POST", 405),  # Method not allowed
            ("PUT", 405),   # Method not allowed
            ("DELETE", 405) # Method not allowed
        ]
        
        for method, expected_status in methods_and_expected:
            response = client.request(method, "/")
            assert response.status_code == expected_status

    def test_content_type_handling(self, client):
        """Test content type handling."""
        # Test JSON content type
        response = client.post(
            "/ollie/classify",
            json={"prompt": "test"},
            headers={"Content-Type": "application/json"}
        )
        
        # Should get 403 (auth required) not 415 (unsupported media type)
        assert response.status_code == 403
        
        # Test invalid content type
        response = client.post(
            "/ollie/classify",
            data="invalid data",
            headers={"Content-Type": "text/plain"}
        )
        
        # Should get 422 (validation error) or 403 (auth required)
        assert response.status_code in [422, 403]
