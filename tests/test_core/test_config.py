"""
Tests for core configuration module.
"""
import os
import pytest
from unittest.mock import patch, MagicMock
from botocore.exceptions import NoCredentialsError, PartialCredentialsError

from app.core.config import (
    initialize_environment,
    initialize_chain_environment,
    get_database_url,
    get_ssm_parameters,
    get_mongo_ssm_parameters,
    extract_api_version_and_model
)


@pytest.mark.unit
class TestCoreConfig:
    """Test cases for core configuration functions."""

    @patch('app.core.config.get_ssm_parameters')
    @patch('app.core.config.AzureOpenAI')
    @patch('app.core.config.APIRouter')
    def test_initialize_environment_success(
        self, 
        mock_api_router, 
        mock_azure_openai, 
        mock_get_ssm_parameters
    ):
        """Test successful environment initialization."""
        # Setup mocks
        mock_get_ssm_parameters.return_value = {
            "openaikey": "test_openai_key",
            "redispassword": "test_redis_password",
            "redishost": "localhost",
            "ai-model-endpoint": "https://test.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-15-preview"
        }
        
        mock_client = MagicMock()
        mock_azure_openai.return_value = mock_client
        
        mock_router = MagicMock()
        mock_api_router.return_value = mock_router
        
        # Test data
        parameter_names = ["openaikey", "redispassword", "redishost", "ai-model-endpoint"]
        
        # Call the function
        result = initialize_environment(parameter_names)
        
        # Assertions
        assert len(result) == 5
        client, router, model_name, redis_password, redis_host = result
        
        assert client == mock_client
        assert router == mock_router
        assert model_name == "gpt-4o"
        assert redis_password == "test_redis_password"
        assert redis_host == "localhost"
        
        # Verify mocks were called
        mock_get_ssm_parameters.assert_called_once_with(parameter_names)
        mock_azure_openai.assert_called_once()
        mock_api_router.assert_called_once()

    @patch('app.core.config.get_ssm_parameters')
    @patch('app.core.config.AzureChatOpenAI')
    def test_initialize_chain_environment_success(
        self, 
        mock_azure_chat_openai, 
        mock_get_ssm_parameters
    ):
        """Test successful chain environment initialization."""
        # Setup mocks
        mock_get_ssm_parameters.return_value = {
            "openaikey": "test_openai_key",
            "ai-model-endpoint": "https://test.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-15-preview",
            "embedding_azure_endpoint": "https://test.openai.azure.com/openai/deployments/text-embedding-3-small/embeddings?api-version=2023-05-15"
        }
        
        mock_model = MagicMock()
        mock_azure_chat_openai.return_value = mock_model
        
        # Test data
        parameter_names = ["openaikey", "ai-model-endpoint", "embedding_azure_endpoint"]
        
        # Call the function
        result = initialize_chain_environment(parameter_names)
        
        # Assertions
        assert result == mock_model
        
        # Verify mocks were called
        mock_get_ssm_parameters.assert_called_once_with(parameter_names)
        mock_azure_chat_openai.assert_called_once()

    @patch.dict(os.environ, {"LOCAL_DB_URL": "mongodb://localhost:27017/test_db"})
    def test_get_database_url_local(self):
        """Test database URL retrieval from local environment."""
        # Call the function
        result = get_database_url()
        
        # Assertions
        assert result == "mongodb://localhost:27017/test_db"

    @patch.dict(os.environ, {}, clear=True)
    @patch('app.core.config.get_mongo_ssm_parameters')
    def test_get_database_url_ssm(self, mock_get_mongo_ssm_parameters):
        """Test database URL retrieval from SSM."""
        # Setup mock
        mock_get_mongo_ssm_parameters.return_value = {
            "central/dburl": "mongodb://prod-cluster.mongodb.net/prod_db"
        }
        
        # Call the function
        result = get_database_url()
        
        # Assertions
        assert result == "mongodb://prod-cluster.mongodb.net/prod_db"
        mock_get_mongo_ssm_parameters.assert_called_once_with(["central/dburl"])

    @patch.dict(os.environ, {}, clear=True)
    @patch('app.core.config.get_mongo_ssm_parameters')
    def test_get_database_url_error(self, mock_get_mongo_ssm_parameters):
        """Test database URL retrieval error handling."""
        # Setup mock to raise exception
        mock_get_mongo_ssm_parameters.side_effect = Exception("SSM error")
        
        # Call the function
        result = get_database_url()
        
        # Assertions
        assert result is None

    @patch('app.core.config.boto3.client')
    def test_get_ssm_parameters_success(self, mock_boto3_client):
        """Test successful SSM parameter retrieval."""
        # Setup mock
        mock_ssm_client = MagicMock()
        mock_boto3_client.return_value = mock_ssm_client
        
        mock_ssm_client.get_parameter.side_effect = [
            {"Parameter": {"Name": "/test/openaikey", "Value": "test_key_1"}},
            {"Parameter": {"Name": "/test/redishost", "Value": "test_host"}}
        ]
        
        # Test data
        parameter_names = ["openaikey", "redishost"]
        
        # Mock environment variables
        with patch.dict(os.environ, {"ENVIRONMENT_NAME": "test", "AWS_REGION": "us-east-1"}):
            result = get_ssm_parameters(parameter_names)
        
        # Assertions
        assert result == {
            "openaikey": "test_key_1",
            "redishost": "test_host"
        }
        
        # Verify SSM client was called correctly
        assert mock_ssm_client.get_parameter.call_count == 2

    @patch('app.core.config.boto3.client')
    def test_get_ssm_parameters_no_credentials(self, mock_boto3_client):
        """Test SSM parameter retrieval with no credentials error."""
        # Setup mock to raise NoCredentialsError
        mock_boto3_client.side_effect = NoCredentialsError()
        
        # Test data
        parameter_names = ["openaikey"]
        
        # Mock environment variables
        with patch.dict(os.environ, {"ENVIRONMENT_NAME": "test", "AWS_REGION": "us-east-1"}):
            with pytest.raises(NoCredentialsError):
                get_ssm_parameters(parameter_names)

    @patch('app.core.config.boto3.client')
    def test_get_ssm_parameters_parameter_not_found(self, mock_boto3_client):
        """Test SSM parameter retrieval when parameter is not found."""
        # Setup mock
        mock_ssm_client = MagicMock()
        mock_boto3_client.return_value = mock_ssm_client
        
        # Mock ParameterNotFound exception
        from botocore.exceptions import ClientError
        mock_ssm_client.get_parameter.side_effect = ClientError(
            {"Error": {"Code": "ParameterNotFound"}}, 
            "GetParameter"
        )
        
        # Test data
        parameter_names = ["nonexistent_param"]
        
        # Mock environment variables
        with patch.dict(os.environ, {"ENVIRONMENT_NAME": "test", "AWS_REGION": "us-east-1"}):
            with pytest.raises(ValueError) as exc_info:
                get_ssm_parameters(parameter_names)
        
        assert "SSM Parameter" in str(exc_info.value)

    @patch('app.core.config.boto3.client')
    def test_get_mongo_ssm_parameters_success(self, mock_boto3_client):
        """Test successful MongoDB SSM parameter retrieval."""
        # Setup mock
        mock_ssm_client = MagicMock()
        mock_boto3_client.return_value = mock_ssm_client
        
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Name": "/test/central/dburl", 
                "Value": "mongodb://test-cluster.mongodb.net/test_db"
            }
        }
        
        # Test data
        parameter_names = ["central/dburl"]
        
        # Mock environment variables
        with patch.dict(os.environ, {"ENVIRONMENT_NAME": "test", "AWS_REGION": "us-east-1"}):
            result = get_mongo_ssm_parameters(parameter_names)
        
        # Assertions
        assert result == {
            "central/dburl": "mongodb://test-cluster.mongodb.net/test_db"
        }

    def test_extract_api_version_and_model_success(self):
        """Test successful API version and model extraction."""
        # Test data
        endpoint = "https://test.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-15-preview"
        
        # Call the function
        model, version = extract_api_version_and_model(endpoint)
        
        # Assertions
        assert model == "gpt-4o"
        assert version == "2024-02-15-preview"

    def test_extract_api_version_and_model_embedding(self):
        """Test API version and model extraction for embedding endpoint."""
        # Test data
        endpoint = "https://test.openai.azure.com/openai/deployments/text-embedding-3-small/embeddings?api-version=2023-05-15"
        
        # Call the function
        model, version = extract_api_version_and_model(endpoint)
        
        # Assertions
        assert model == "text-embedding-3-small"
        assert version == "2023-05-15"

    def test_extract_api_version_and_model_invalid_format(self):
        """Test API version and model extraction with invalid format."""
        # Test data with invalid format
        endpoint = "https://invalid-endpoint.com/api"
        
        # Call the function - should handle gracefully
        try:
            model, version = extract_api_version_and_model(endpoint)
            # If no exception, check that reasonable defaults are returned
            assert isinstance(model, str)
            assert isinstance(version, str)
        except Exception:
            # If exception is raised, that's also acceptable for invalid input
            pass

    @patch('app.core.config.get_ssm_parameters')
    def test_initialize_environment_missing_parameters(self, mock_get_ssm_parameters):
        """Test environment initialization with missing parameters."""
        # Setup mock to return incomplete parameters
        mock_get_ssm_parameters.return_value = {
            "openaikey": "test_key"
            # Missing other required parameters
        }
        
        # Test data
        parameter_names = ["openaikey", "redispassword", "redishost", "ai-model-endpoint"]
        
        # Call the function and expect KeyError
        with pytest.raises(KeyError):
            initialize_environment(parameter_names)

    @patch('app.core.config.get_ssm_parameters')
    def test_initialize_chain_environment_missing_parameters(self, mock_get_ssm_parameters):
        """Test chain environment initialization with missing parameters."""
        # Setup mock to return incomplete parameters
        mock_get_ssm_parameters.return_value = {
            "openaikey": "test_key"
            # Missing other required parameters
        }
        
        # Test data
        parameter_names = ["openaikey", "ai-model-endpoint", "embedding_azure_endpoint"]
        
        # Call the function and expect KeyError
        with pytest.raises(KeyError):
            initialize_chain_environment(parameter_names)

    @patch.dict(os.environ, {"ENVIRONMENT_NAME": "", "AWS_REGION": ""})
    @patch('app.core.config.boto3.client')
    def test_get_ssm_parameters_missing_env_vars(self, mock_boto3_client):
        """Test SSM parameter retrieval with missing environment variables."""
        # Test data
        parameter_names = ["openaikey"]
        
        # Call the function - should handle missing env vars gracefully
        try:
            result = get_ssm_parameters(parameter_names)
            # Function should either work with defaults or raise appropriate error
        except Exception as e:
            # Acceptable to raise exception for missing required env vars
            assert isinstance(e, (ValueError, TypeError, AttributeError))

    def test_extract_api_version_and_model_edge_cases(self):
        """Test API version and model extraction edge cases."""
        # Test with empty string
        try:
            model, version = extract_api_version_and_model("")
            assert isinstance(model, str)
            assert isinstance(version, str)
        except Exception:
            pass  # Acceptable to raise exception for empty input
        
        # Test with None
        try:
            model, version = extract_api_version_and_model(None)
            assert isinstance(model, str)
            assert isinstance(version, str)
        except Exception:
            pass  # Acceptable to raise exception for None input
